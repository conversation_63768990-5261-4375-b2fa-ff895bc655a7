<?php
	$root=".";
	require($root."/php/init.php");


$page=[
		"title"=>tr("Dashboard"),
		
	];


set_lang(["week","month"],["اسبوع","شهر"],["week","month"]);
	
?>
<!DOCTYPE html>
<html <?= $local ?>>
	<?php require($root."/inc/head.php") ?>
	



	<body class=" <?= isset($page['class'])?$page['class']:'' ?> " >
		<?php require($root."/inc/header.php") ?>
		<br>
<div class="row">
	<div class="col">
		<?php $display->display_messages() ?>
		<div id="notify"></div>
		<style>
		.quick-link{
		/*max-width: 18rem !important;*/
		padding: 0;
		margin: 1px
		}
		.quick-link div{
		cursor: pointer;
		}
		.quick-link div:hover{
		opacity: 0.7;
		}
		.quick-link .card .card-body{
		padding: 20px 0 ;
		}

		/* Enhanced Calendar Styling */
		#full-calendar {
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
		}

		.fc-event {
			border-radius: 4px !important;
			border: none !important;
			padding: 2px 4px !important;
			font-size: 0.85em !important;
			font-weight: 500 !important;
			box-shadow: 0 1px 3px rgba(0,0,0,0.12) !important;
			transition: all 0.2s ease !important;
		}

		.fc-event:hover {
			transform: translateY(-1px) !important;
			box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
		}

		.fc-day-grid-event {
			margin: 1px 2px !important;
		}

		.fc-title {
			font-weight: 600 !important;
		}

		/* Booking event specific styling */
		.fc-event[style*="#28a745"] {
			background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
		}

		.fc-event[style*="#dc3545"] {
			background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
		}

		.fc-event[style*="#ffc107"] {
			background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%) !important;
			color: #212529 !important;
		}

		.fc-event[style*="#007bff"] {
			background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
		}

		.fc-event[style*="#1700C7"] {
			background: linear-gradient(135deg, #1700C7 0%, #6f42c1 100%) !important;
		}

		/* Calendar header styling */
		.fc-header-toolbar {
			margin-bottom: 1.5em !important;
		}

		.fc-button {
			background: #2C3E50 !important;
			border-color: #2C3E50 !important;
			font-size: 0.875rem !important;
		}

		.fc-button:hover {
			background: #34495e !important;
			border-color: #34495e !important;
		}

		.fc-today {
			background: rgba(44, 62, 80, 0.1) !important;
		}
		</style>
		<div class="row">
			<div class="col">
				<div class="card shadow">
		
					<div class="card-body">
						<div class="row">
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white mb-3 rounded-0 shadow" style="background: #59AEAB" onclick="window.open('<?= $app_url ?>/pos/', '_blank')">
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-shopping-basket"></i></h3>
										<p class="card-text text-center"><?= tr('Point of sale') ?></p>
									</div>
								</div>
							</div>
							
							<?php if (can("view-items|manage-items")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<a href="<?= $app_url."/products/" ?>">
									<div class="card text-white mb-3 rounded-0 shadow" style="background: #3268AB">
										<div class="card-body">
											<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-barcode"></i></h3>
											<p class="card-text text-center"><?= tr('Items') ?></p>
										</div>
									</div>
								</a>
							</div>
							<?php endif ?>
							<?php if (can("view-purchases|manage-purchases")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<a href="<?= $app_url."/purchases/" ?>">
									<div class="card text-white mb-3 rounded-0 shadow" style="background: #F3AF58">
										<div class="card-body">
											<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-box"></i></h3>
											<p class="card-text text-center"><?= tr('Purchases') ?></p>
										</div>
									</div>
								</a>
							</div>
							<?php endif ?>
							<?php if (can("view-sales|manage-sales")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #26A66E" onclick="location.href='<?= $app_url ?>/sales/'">
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-shopping-basket"></i></h3>
										<p class="card-text text-center"><?= tr('Sales') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							<?php if (can("manage-payments|add-payments")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #237385" onclick='window.open("<?= $app_url ?>/payments/?v=new&popup=1&noreload=1","_blank","height=820px width=700px");'>
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-money-bill-alt"></i></h3>
										<p class="card-text text-center"><?= tr('Payments') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							<?php if (can("manage-customers-payments|add-customers-payments")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #2F8523" onclick='window.open("<?= $app_url ?>/customers/payments.php?v=new&popup=1&noreload=1","_blank","height=820px width=750px");'>
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-file-invoice-dollar"></i></h3>
										<p class="card-text text-center"><?= tr('Receipt voucher') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							<?php if (can("add-refunds|manage-refunds")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #465EC7" onclick='window.open("<?= $app_url ?>/refunds/?v=new&popup=1&noreload=1","_blank");'>
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-undo-alt"></i></h3>
										<p class="card-text text-center"><?= tr('Refund') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							<?php if (can("manage-repair")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #C10E73" onclick='window.open("<?= $app_url ?>/repair/index.php?v=new&popup=1&noreload=1","_blank","height=820px width=750px");'>
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-tools"></i></h3>
										<p class="card-text text-center"><?= tr('Repair') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							<?php if (can("view-customers|manage-customers")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link " >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #956989" onclick="location.href='<?= $app_url ?>/customers/'">
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-users"></i></h3>
										<p class="card-text text-center"><?= tr('Customers') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							<?php if (can("view-expenses|manage-expenses")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #C79E46" onclick="location.href='<?= $app_url ?>/expenses/'">
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-comment-dollar"></i></h3>
										<p class="card-text text-center"><?= tr('Expenses') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>

							<?php if (can("add-contracts|view-contracts|manage-contracts")): ?>
							<div class="col-md-3 col-lg-2 col-xl-1 quick-link" >
								<div class="card text-white  mb-3 rounded-0 shadow" style="background: #46C79B" onclick="location.href='<?= $app_url ?>/contract/'">
									<div class="card-body">
										<h3 class="card-title text-center"><i style="font-size:1.3rem" class="fas fa-clipboard"></i></h3>
										<p class="card-text text-center"><?= tr('Contracts') ?></p>
									</div>
								</div>
							</div>
							<?php endif ?>
							
							<?php 
							do_hook("quick-links-dashboard");
							 ?>
							
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row row-eq-height">
			<div class="col-lg-3 ">
				<div class="card shadow">
					<div class="card-header">
						<h6 class="card-title"><?= tr("Visits") ?></h6>
					</div>
					<div class="card-body">
						<canvas id="sales-chart" ></canvas>
					</div>
				</div>
			</div>
			<?php if (can('calendar')): ?>
			<div class="col-lg-9">
				<!-- Booking Statistics -->
				<?php
				$today = date('Y-m-d');
				$booking_stats = [
					'total' => $db->single("SELECT COUNT(*) FROM booking WHERE deleted_01 = 0"),
					'active' => $db->single("SELECT COUNT(*) FROM booking WHERE deleted_01 = 0 AND status = 'Booked' AND start_date <= ? AND end_date >= ?", [$today, $today]),
					'upcoming' => $db->single("SELECT COUNT(*) FROM booking WHERE deleted_01 = 0 AND status = 'Booked' AND start_date > ?", [$today]),
					'completed' => $db->single("SELECT COUNT(*) FROM booking WHERE deleted_01 = 0 AND status = 'Completed'"),
					'overdue' => $db->single("SELECT COUNT(*) FROM booking WHERE deleted_01 = 0 AND status = 'Booked' AND end_date < ?", [$today])
				];
				?>
				<div class="row mb-3">
					<div class="col">
						<div class="card bg-light border-0">
							<div class="card-body py-2">
								<div class="row text-center">
									<div class="col">
										<small class="text-muted"><?= tr('Total Bookings') ?></small>
										<div class="h6 mb-0 text-primary"><?= $booking_stats['total'] ?></div>
									</div>
									<div class="col">
										<small class="text-muted"><?= tr('Active') ?></small>
										<div class="h6 mb-0 text-warning"><?= $booking_stats['active'] ?></div>
									</div>
									<div class="col">
										<small class="text-muted"><?= tr('Upcoming') ?></small>
										<div class="h6 mb-0 text-info"><?= $booking_stats['upcoming'] ?></div>
									</div>
									<div class="col">
										<small class="text-muted"><?= tr('Completed') ?></small>
										<div class="h6 mb-0 text-success"><?= $booking_stats['completed'] ?></div>
									</div>
									<div class="col">
										<small class="text-muted"><?= tr('Overdue') ?></small>
										<div class="h6 mb-0 text-danger"><?= $booking_stats['overdue'] ?></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="card shadow">
					<div class="card-header d-flex justify-content-between align-items-center">
						<h5 class="card-title mb-0">
							<i class="fas fa-calendar-alt mr-2"></i><?= tr('Calendar & Bookings') ?>
						</h5>
						<div>
							<button role="button" data-toggle="modal" data-target="#new-date-modal" class="btn btn-primary btn-sm" style="background: #2C3E50">
								<i class="fas fa-plus mr-1"></i><?= tr('New Event') ?>
							</button>
							<?php if (can('manage-booking')): ?>
							<a href="<?= $app_url ?>/apps/booking/" class="btn btn-info btn-sm">
								<i class="fas fa-calendar-check mr-1"></i><?= tr('Manage Bookings') ?>
							</a>
							<?php endif ?>
						</div>
					</div>
					<div class="card-body">
						<!-- Calendar Legend -->
						<div class="row mb-3">
							<div class="col-12">
								<div class="d-flex flex-wrap align-items-center">
									<small class="text-muted mr-3"><strong><?= tr('Legend') ?>:</strong></small>
									<span class="badge mr-2 mb-1" style="background: #28a745; color: white;">
										<i class="fas fa-check-circle mr-1"></i><?= tr('Completed Booking') ?>
									</span>
									<span class="badge mr-2 mb-1" style="background: #ffc107; color: #212529;">
										<i class="fas fa-clock mr-1"></i><?= tr('Active Booking') ?>
									</span>
									<span class="badge mr-2 mb-1" style="background: #007bff; color: white;">
										<i class="fas fa-calendar-alt mr-1"></i><?= tr('Upcoming Booking') ?>
									</span>
									<span class="badge mr-2 mb-1" style="background: #dc3545; color: white;">
										<i class="fas fa-exclamation-triangle mr-1"></i><?= tr('Overdue Booking') ?>
									</span>
									<span class="badge mr-2 mb-1" style="background: #1700C7; color: white;">
										<i class="fas fa-bell mr-1"></i><?= tr('Reminder') ?>
									</span>
									<span class="badge mr-2 mb-1" style="background: #6c757d; color: white;">
										<i class="fas fa-calendar mr-1"></i><?= tr('Event') ?>
									</span>
								</div>
							</div>
						</div>

						<div id="full-calendar" style="min-height: 600px;"></div>
					</div>
				</div>
			</div>
			<?php endif ?>
			
		</div>
	</div>
</div>
		

		<?php require_once($root."/inc/footer.php") ?>
		<?php require_once($root."/inc/scripts.php") ?>
		<script src="<?= $root ?>/assets/vendor/chart-js/chartjs.min.js"></script>

		  <!-- <link href='<?= $root ?>/assets/vendor/fullcalendar/packages/core/main.css' rel='stylesheet' /> -->
<!-- <link href='<?= $root ?>/assets/vendor/fullcalendar/packages/daygrid/main.css' rel='stylesheet' /> -->
<!-- <link href='<?= $root ?>/assets/vendor/fullcalendar/packages/timegrid/main.css' rel='stylesheet' /> -->
<!-- <link href='<?= $root ?>/assets/vendor/fullcalendar/packages/list/main.css' rel='stylesheet' /> -->

    <script src='<?= $root ?>/assets/vendor/fullcalendar/packages/core/main.js'></script>
<script src='<?= $root ?>/assets/vendor/fullcalendar/packages/interaction/main.js'></script>
<script src='<?= $root ?>/assets/vendor/fullcalendar/packages/daygrid/main.js'></script>
<script src='<?= $root ?>/assets/vendor/fullcalendar/packages/timegrid/main.js'></script>
<script src='<?= $root ?>/assets/vendor/fullcalendar/packages/list/main.js'></script>

		<?php 

  $today = strtotime("today");
  $day1  = strtotime("-1 week  +1 day",$today); $day1 = date("Y-m-d",$day1); 
  $day2  = strtotime("-1 week  +2 day",$today); $day2 = date("Y-m-d",$day2); 
  $day3  = strtotime("-1 week  +3 day",$today); $day3 = date("Y-m-d",$day3); 
  $day4  = strtotime("-1 week  +4 day",$today); $day4 = date("Y-m-d",$day4); 
  $day5  = strtotime("-1 week  +5 day",$today); $day5 = date("Y-m-d",$day5); 
  $day6  = strtotime("-1 week  +6 day",$today); $day6 = date("Y-m-d",$day6); 
  $day7  = strtotime("-1 week  +7 day",$today); $day7 = date("Y-m-d",$day7);

  $week_date_array=[
  	$day1,
  	$day2,
  	$day3,
  	$day4,
  	$day5,
  	$day6,
  	$day7
  ];

  $week_bills_array=[
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day1%'")->fetchColumn(),
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day2%'")->fetchColumn(),
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day3%'")->fetchColumn(),
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day4%'")->fetchColumn(),
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day5%'")->fetchColumn(),
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day6%'")->fetchColumn(),
  	$conn->query("SELECT count(*) FROM bills WHERE created_at LIKE '%$day7%'")->fetchColumn(),
  ];




  $week_days_array=[
  		$lang[date('D',strtotime($day1))],
	  	$lang[date('D',strtotime($day2))],
	  	$lang[date('D',strtotime($day3))],
	  	$lang[date('D',strtotime($day4))],
	  	$lang[date('D',strtotime($day5))],
	  	$lang[date('D',strtotime($day6))],
	  	$lang['today']
	  ];

   ?>

		<script>
			$(document).ready(function() {
				chartColor = "#FFFFFF";
				  ctx = document.getElementById('sales-chart').getContext("2d");
				  
				    gradientStroke = ctx.createLinearGradient(500, 0, 100, 0);
				    gradientStroke.addColorStop(0, '#80b6f4');
				    gradientStroke.addColorStop(1, chartColor);

				    gradientFill = ctx.createLinearGradient(0, 170, 0, 50);
				    gradientFill.addColorStop(0, "rgba(128, 182, 244, 0)");
				    gradientFill.addColorStop(1, "rgba(249, 99, 59, 0.40)");

				    myChart = new Chart(ctx, {
				      type: 'bar',
				    
				      data: {
				        labels: <?= json_encode($week_days_array); ?>,
				        datasets: [{
				          label: "<?= escap($lang['psales']) ?>",
				          // borderColor: "#1C5C84",
				          backgroundColor:'#2C3E50',
				          pointRadius: 10,
				          pointHoverRadius: 13,
				          fill: true,
				          borderWidth: 2,
				          data: <?= json_encode($week_bills_array); ?>
				        }]
				      },
				      options: {

				        legend: {

				          display: false

				        },

				        

				        tooltips: {
				          enabled: true,
				          intersect:true,
				          position:'average',
				          backgroundColor:'rgba(0,0,0,0.8)'
				        },

				        scales: {
				          yAxes: [{

				            ticks: {
				              fontColor: "#9f9f9f",
				              beginAtZero: false,
				              maxTicksLimit: 5,
				              //padding: 20
				            },
				            gridLines: {
				              drawBorder: false,
				              zeroLineColor: "transparent",
				              color: '#D2D2D2'
				            }

				          }],

				          xAxes: [{
				          
				            gridLines: {
				              drawBorder: false,
				              color: '#328A9A',
				              zeroLineColor: "transparent",
				              display: false,
				            },
				            ticks: {
				    
				              fontColor: "#9f9f9f"
				            }
				          }]
				        },
				      }
				    });









			});
			</script>

			<?php if (can('calendar')): ?>
			<?php
			// Get calendar events
			$calendar_events = $db->query("SELECT time_date AS start, title, color, id, '' as url, 'calendar' as type FROM calendar WHERE deleted_01 = 0");

			// Get customer reminders
			$customer_reminders = $db->query("SELECT send_date as start, concat('".tr('Reminder').":',(SELECT name FROM customers WHERE id = customer_id)) as title , '#1700C7' as color,id, concat('".$app_url."/customers/?v=view&i="."',customer_id) as url, 'reminder' as type FROM customer_reminder");

			// Get booking events with enhanced information
			$booking_events = $db->query("SELECT
			    b.start_date as start,
			    b.end_date as end,
			    CONCAT(p.".pname("name","name2").", ' - ', c.name) as title,
			    CASE
			        WHEN b.status = 'Completed' THEN '#28a745'
			        WHEN b.status = 'Booked' AND DATE(b.end_date) < CURDATE() THEN '#dc3545'
			        WHEN b.status = 'Booked' AND DATE(b.start_date) <= CURDATE() AND DATE(b.end_date) >= CURDATE() THEN '#ffc107'
			        ELSE '#007bff'
			    END as color,
			    b.bill_id as id,
			    CONCAT('".$app_url."/sales/view.php?i=', b.bill_id) as url,
			    'booking' as type,
			    b.status,
			    c.name as customer_name,
			    p.".pname("name","name2")." as product_name
			FROM booking b
			LEFT JOIN products p ON b.p_id = p.id
			LEFT JOIN customers c ON b.customer_id = c.id
			LEFT JOIN bills bill ON bill.id = b.bill_id
			WHERE b.deleted_01 = 0 AND ".branch()->query("bill.branch_id"));

			// Combine all events
			$all_events = array_merge($calendar_events, $customer_reminders, $booking_events);
			?>

			<script>
				try {
					var events = <?= json_encode($all_events) ?>;
					console.log('Calendar events loaded:', events.length, 'events');
					var calendarEl = document.querySelector('#full-calendar');

					if (calendarEl) {
						calendar = new FullCalendar.Calendar(calendarEl, {
	                plugins: [ 'dayGrid', 'interaction' ],
	                header: {
	                    left: 'prev,next today',
	                    center: 'title',
	                    right: 'dayGridMonth,dayGridWeek,dayGridDay'
	                },
	                defaultDate: '<?= date("Y-m-d") ?>',
	                editable: false,
	                events: events,
	                eventLimit: true,
	                selectable: true,
	                height: 'auto',
	                aspectRatio: 1.8,
	                buttonText:{
		      	today:    '<?= $lang['today'] ?>',
				  month:    '<?= $lang['month'] ?>',
				  week:     '<?= $lang['week'] ?>',
				  day:      '<?= $lang['day'] ?>',
				  list:     '<?= $lang['list'] ?>'
		      },
		       locale: '<?= ($isRtl)?"ar-kw":"en" ?>',
		       eventClick: function(info) {
		       	var eventType = info.event.extendedProps.type;

		       	if (eventType === 'booking') {
		       		// For booking events, navigate to sales invoice
		       		window.open(info.event.url, '_blank');
		       	} else if (eventType === 'reminder') {
		       		// For reminder events, navigate to customer page
		       		if (info.event.url) {
		       			window.open(info.event.url, '_blank');
		       		}
		       	} else if (eventType === 'calendar') {
		       		// For regular calendar events, show event details
		       		view_event.get_event(info.event.id);
		       	}

		       	// Prevent default link behavior
		       	info.jsEvent.preventDefault();
			  },
			  eventRender: function(info) {
			  	var eventType = info.event.extendedProps.type;

			  	// Add custom styling based on event type
			  	if (eventType === 'booking') {
			  		info.el.style.cursor = 'pointer';
			  		info.el.title = 'Click to view invoice - Status: ' + (info.event.extendedProps.status || 'Unknown');
			  	} else if (eventType === 'reminder') {
			  		info.el.style.cursor = 'pointer';
			  		info.el.title = 'Click to view customer details';
			  	} else {
			  		info.el.style.cursor = 'pointer';
			  		info.el.title = 'Click to view event details';
			  	}
			  }

	            });

					calendar.render();
				} else {
					console.error('Calendar element not found');
				}
			} catch (error) {
				console.error('Error initializing calendar:', error);
			}


			
			
				// var calendarEl = document.getElementById('full-calendar');
		  //   var maincalendar = new FullCalendar.Calendar(calendarEl, {
		  //     plugins: [ 'interaction', 'dayGrid', 'timeGrid', 'list' ],
		  //     header: {
		  //       left: 'prev,next today',
		  //       center: 'title',
		  //       right: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
		  //     },
		  //     buttonText:{
		  //     	today:    '<?= $lang['today'] ?>',
				//   month:    '<?= $lang['month'] ?>',
				//   week:     '<?= $lang['week'] ?>',
				//   day:      '<?= $lang['day'] ?>',
				//   list:     '<?= $lang['list'] ?>'
		  //     },
		  //     locale: '<?= ($isRtl)?"ar-kw":"en" ?>',
		  //     defaultDate: '<?php echo date("Y-m-d"); ?>',
		  //     navLinks: true, // can click day/week names to navigate view
		  //     editable: false,
		  //     selectable: true,
		  //     selectHelper: true,
		  //     eventLimit: true,
		  //     select: function(start, end) {


    //           },
    //           dateClick: function() {
			
			 //  },
			
			 //  eventClick: function(info) {
			 //  	console.log(info.event.id)
			 //  	view_event.get_event(info.event.id)
			 //  },
		  //     events: {
		  //     	url: '<?= $app_url ?>/calendar/ajax/calendar.php?func=get',
		  //     },
		      
		  //   });

		  //   maincalendar.render();

		</script>
		<?php endif ?>

	<?php if (can('calendar')): ?>

		
		<?php require_once($root."/calendar/inc/new-date.inc.php") ?>
		<?php require_once($root."/calendar/inc/view-event-modal.inc.php") ?>
	<?php endif ?>
	
	</body>
</html>