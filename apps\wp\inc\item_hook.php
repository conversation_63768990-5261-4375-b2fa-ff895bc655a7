<?php 


function wp_create_item($id){
	
	global $db,  $woocommerce;

	$data = $db->row("SELECT * FROM products WHERE id = ? ",[$id]);

	$tax = $db->row("SELECT * FROM taxes WHERE id = ?",[$data['tax_id']]);

	$brand =  $db->row("SELECT * FROM brands WHERE id = ? ",[$data['brand_id']]);
	$tags=[];
	if ($brand['website_id']==0) {
		$post = [
		    "name" => $brand['name'],
		];

		$res = $woocommerce->post('products/tags', $post);

		$db->query("UPDATE brands SET website_id = ? WHERE id = ?  ",[$res->id,$brand['id']]);

		$tags[]["id"]=$res->id;
	}else{
		$tags[]["id"]=$brand['website_id'];
	}

	$category_data = $db->row("SELECT * FROM categories WHERE id = ? ",[$data['category_id']]);

	if ($category_data['website']==0) { // update category
		
		$post = [
		    "name" => $category_data['name'],
		    "parent"=> $category_data['parent_id'],
		    'image' => []
		];

		$res = $woocommerce->post('products/categories', $post);

		$db->query("UPDATE categories SET website=1, website_id = ? WHERE id = ?  ",[$res->id,$category_data['id']]);

		$category_data = $db->row("SELECT * FROM categories WHERE id = ? ",[$data['category_id']]);
	}

	$images =[];

	foreach ((is_json($data['images'])?json_decode($data['images']):[]) as $key => $img) {

		$image = $db->row("SELECT * FROM uploads WHERE id = ? ",[$img]);

		if ($image) {
			$post=[
				"title"=>$image['file_name'],
				"src"=>file_get($image['id']),
				"alt"=> $data['name']
			];

			$res = $woocommerce->post('media',$post);

			$db->query("UPDATE uploads SET website_id=? WHERE id = ?",[$res->id,$image['id']]);
		}

		$images[]['id']=$res->id;
	}
	
	$has_attributes=false;
	$attributes = [
		[
            'id' => 1,
            'position' => 0,
            'visible' => true,
            'variation' => true,
            'options' => [
          
            ]
        ],
		[
            'id' => 2,
            'position' => 0,
            'visible' => true,
            'variation' => true,
            'options' => [
                
            ]
        ]
	];

	foreach (is_json($data['colors'])?json_decode($data['colors']):[] as $key => $color) {
		$has_attributes=true;
		$attributes[0]['options'][]=$color;
	}

	if (count($attributes[0]['options'])==0) {
		$attributes[0]['visible']=false;
	}

	foreach (is_json($data['sizes'])?json_decode($data['sizes']):[] as $key => $color) {
		$has_attributes=true;
		$attributes[1]['options'][]=$color;
	}

	if (count($attributes[1]['options'])==0) {

		$attributes[1]['visible']=false;
	}

	$default_attributes=[];

	foreach ($attributes as $key => $attr) {
		if (count($attr['options'])) {
			$default_attributes[]=[
				"id"=>$attr['id'],
				"option"=>$attr['options'][0],
			];
		}
	}

	$post = [
	    'name' => strlen($data['name2'])>2?$data['name2']:$data['name'],
	    'type' => $has_attributes?'variable':'simple',
	    "sku"=>$data['barcode'],
		"regular_price"=> float($data['price']+($data['price']*$tax['percent']/100),3),
		"purchasable"=> true,
		"manage_stock"=> false,
		"stock_quantity"=> null,
		"stock_status"=> "instock",
	    'description' => $data['long_description'],
	    'short_description' => $data['description'],
	    "tags"=>$tags,
	    'categories' => [
	        [
	            'id' => $category_data['website_id']
	        ],
	    ],
	    'images' => $images,
	    'attributes' => $attributes,
	    'default_attributes' => $default_attributes
	];

	$res = $woocommerce->post('products', $post);



	$db->query("UPDATE products SET website_id = ? WHERE id = ?  ",[$res->id,$id]);

	if ($has_attributes) {

		$vlist = $woocommerce->get('products/'.$res->id.'/variations');
		$vdelete=[];
		foreach ($vlist as $key => $vitem) {
			$vdelete[]=$vitem->id;
		}

		$vpost = [
			'delete' => $vdelete,
		    'create' => [
		        [
		            "regular_price"=> float($data['price']+($data['price']*$tax['percent']/100),3),
					"manage_stock"=> false,
					"stock_quantity"=> null,
					"stock_status"=> "instock",
		        ],
		      
		    ],
		];

		$woocommerce->post('products/'.$res->id.'/variations/batch', $vpost);

		
		

	}

	if (strlen($data['name2'])) {
		$woocommerce->post('translate', [
			"name"=>$data['name2'],
			"lang"=>[
				"en_us_ar"=>$data['name']
			]

		]);
	}
}

function wp_update_item($id){
	global $db,  $woocommerce;

	$data = $db->row("SELECT * FROM products WHERE id = ? ",[$id]);

	$tax = $db->row("SELECT * FROM taxes WHERE id = ?",[$data['tax_id']]);

	$brand =  $db->row("SELECT * FROM brands WHERE id = ? ",[$data['brand_id']]);
	$tags=[];
	if ($brand['website_id']==0) {
		$post = [
		    "name" => $brand['name'],
		];

		$res = $woocommerce->post('products/tags', $post);

		$db->query("UPDATE brands SET website_id = ? WHERE id = ?  ",[$res->id,$brand['id']]);

		$tags[]["id"]=$res->id;
	}else{
		$tags[]["id"]=$brand['website_id'];
	}

	$category_data = $db->row("SELECT * FROM categories WHERE id = ? ",[$data['category_id']]);

	if ($category_data['website']==0) { // update category
		
		$post = [
		    "name" => $category_data['name'],
		    "parent"=> $category_data['parent_id'],
		    'image' => []
		];

		$res = $woocommerce->post('products/categories', $post);

		$db->query("UPDATE categories SET website=1, website_id = ? WHERE id = ?  ",[$res->id,$category_data['id']]);

		$category_data = $db->row("SELECT * FROM categories WHERE id = ? ",[$data['category_id']]);
	}

	$images =[];

	foreach ((is_json($data['images'])?json_decode($data['images']):[]) as $key => $img) {
		$image = $db->row("SELECT * FROM uploads WHERE id = ? ",[$img]);

		if ( $image['website_id']==0) {
			$post=[
				"title"=>$image['file_name'],
				"src"=>file_get($image['id']),
				"alt"=> $data['name']
			];

			$res = $woocommerce->post('media',$post);

			$db->query("UPDATE uploads SET website_id=? WHERE id = ?",[$res->id,$image['id']]);

			$images[]['id']=$res->id;
		}else{
			$images[]['id']=$db->single("SELECT website_id FROM uploads WHERE id = ? ",[$img]);
		}

		

		
	}

	// var_dump($images);
	// 
	

	$has_attributes=false;

	$attributes = [
		[
            'id' => 1,
            'position' => 0,
            'visible' => true,
            'variation' => true,
            'options' => [
          
            ]
        ],
		[
            'id' => 2,
            'position' => 0,
            'visible' => true,
            'variation' => true,
            'options' => [
                
            ]
        ]
	];

	foreach (is_json($data['colors'])?json_decode($data['colors']):[] as $key => $color) {
		$attributes[0]['options'][]=$color;
		$has_attributes=true;
	}

	if (count($attributes[0]['options'])==0) {
		$attributes[0]['visible']=false;
	}

	foreach (is_json($data['sizes'])?json_decode($data['sizes']):[] as $key => $color) {
		$attributes[1]['options'][]=$color;
		$has_attributes=true;
	}

	if (count($attributes[1]['options'])==0) {
		$attributes[1]['visible']=false;
	}

	$default_attributes=[];
	
	foreach ($attributes as $key => $attr) {
		if (count($attr['options'])) {
			$default_attributes[]=[
				"id"=>$attr['id'],
				"option"=>$attr['options'][0],
			];
		}
	}

	$data['specifications'] = is_json($data['specifications'])?json_decode($data['specifications'],true):[];
	$specifications_list = [];
	for ($i=0; $i < count($data['specifications'] ); $i++) { 
		
		
		$spc = $db->row(
			"SELECT ".pname("sp.name","sp.name_en")."  AS name, ".pname("spg.name","spg.name_en")." AS gname
			FROM specifications sp 
			LEFT JOIN specifications_groups spg 
			ON sp.group_id = spg.id
			WHERE sp.id = ?"
		,[$data['specifications'][$i]['label']]);

		$specifications_list[$spc['gname']][]=[
			"label"=>$spc['name'],
			
			"description"=>$data['specifications'][$i]['description'],
		];
	}

	$spc_html ='';

	 foreach ($specifications_list as $key => $spcs){
	   
	        $spc_html.='<table class="table table-sm table-borderless table-striped">';
	             $spc_html.='<thead class="bg-light">';
	                 $spc_html.='<tr>';
	                     $spc_html.='<td colspan="2">';
	                      $spc_html.=escap($key);
	                 	 $spc_html.='</td>';
	                 $spc_html.='</tr>';
	             $spc_html.='</thead>';
	             $spc_html.='<tbody>';
	                 foreach ($spcs as $key => $spc){

	                
	                     $spc_html.='<tr>
	                        <td>'. escap($spc['label']). '</td>
	                        <td>'.escap($spc['description']).'</td>
	              
	                    </tr>';
	                }
	                
	             $spc_html.='</tbody>
	        </table>';
	  
	} 

	$post = [
	    'name' => strlen($data['name2'])>2?$data['name2']:$data['name'],
	    'type' => $has_attributes?'variable':'simple',
	    "sku"=>$data['barcode'],
		"regular_price"=> float($data['price']+($data['price']*$tax['percent']/100),3),
		"purchasable"=> true,
		"manage_stock"=> false,
		"stock_quantity"=> null,
		"stock_status"=> "instock",
	    'description' => $data['long_description'].'<br>'.$spc_html,
	    'short_description' => $data['description'],
	    "tags"=>$tags,
	    'categories' => [
	        [
	            'id' => $category_data['website_id']
	        ],
	    ],
	    'images' => $images,
	    'attributes' => $attributes,
	    'default_attributes' => $default_attributes
	];

	$res = $woocommerce->put('products/'.$data['website_id'], $post);

	if ($has_attributes) {

		$vlist = $woocommerce->get('products/'.$res->id.'/variations');
		$vdelete=[];
		foreach ($vlist as $key => $vitem) {
			$vdelete[]=$vitem->id;
		}

		$vpost = [
			'delete' => $vdelete,
		    'create' => [
		        [
		            "regular_price"=> float($data['price']+($data['price']*$tax['percent']/100),3),
					"manage_stock"=> false,
					"stock_quantity"=> null,
					"stock_status"=> "instock",
		        ],
		      
		    ],
		];

		$woocommerce->post('products/'.$res->id.'/variations/batch', $vpost);

	}


	if (strlen($data['name2'])>2) {
			$woocommerce->post('translate', [
				"name"=>$data['name2'],
				"lang"=>[
					"en_us_ar"=>$data['name']
				]

			]);

			
		}
	// $db->query("UPDATE products SET website_id = ? WHERE id = ?  ",[$res->id,$id]);
}


add_hook("new_item_field",function(){
	$html = "";
	$label1=tr("Website");
	$html.=<<<HTML
	<div class="card card-body shadow">
		<div class="form-check">
			<label class="form-check-label">
				<input type="checkbox" name="website" class=""  >
					{$label1}
			</label>
		</div>
	</div>
	HTML;

	echo $html;
});



add_hook("edit_item_field",function($data){
	$db = new PDO\DB;
	$html = "";
	$label1=tr("Website");
	$checked=$data['website']==1?'checked':'';
	$html.=<<<HTML
	<div class="card card-body shadow">
		<div class="form-check">
			<label class="form-check-label">
				<input type="checkbox" name="website" class="" {$checked}  >
					{$label1}
			</label>
		</div>
	</div>
	HTML;

	echo $html;
});




add_hook("item_created",function($id){
	
	$db = new PDO\DB;
	$website  =isset($_POST['website'])?1:0;

	$db->query("UPDATE products SET website = ? WHERE id = ?  ",[$website,$id]);

	if ($website==1) {

		wp_create_item($id);

	}
});


add_hook("item_additional_updated",function($id){
	global $woocommerce;
	$db = new PDO\DB;
	$website  =isset($_POST['website'])?1:0;

	$data = $db->row("SELECT * FROM products WHERE id = ? ",[$id]);

	$db->query("UPDATE products SET website = ? WHERE id = ? ",[$website,$id]);

	if ($website==1 && $data['website']==0) {

		wp_create_item($id);

	}elseif ($website==0 && $data['website']==1) {
		$woocommerce->delete('products/'.$data['website_id'], ['force' => true]);
	}

});


add_hook("item_updated",function($id){
	global $woocommerce;
	$db = new PDO\DB;

	$data = $db->row("SELECT * FROM products WHERE id = ? ",[$id]);

	if ($data['website']==1) {

		// $woocommerce->delete('products/'.$data['website_id'], ['force' => true]);
		wp_update_item($id);

	}

});


add_hook("item_image_created",function($parms){
	global $woocommerce;
	$db = new PDO\DB;

	$data = $db->row("SELECT * FROM products WHERE id = ? ",[$parms['item_id']]);

	if ($data['website']==1) {

		$image = $db->row("SELECT * FROM uploads WHERE id = ? ",[$parms['image_id']]);

		if ($image) {
			$post=[
				"title"=>$image['file_name'],
				"src"=>file_get($image['id']),
			];

			$res = $woocommerce->post('media',$post);

			$db->query("UPDATE uploads SET website_id=? WHERE id = ?",[$res->id,$image['id']]);

			// $woocommerce->delete('products/'.$data['website_id'], ['force' => true]);
			wp_update_item($data['id']);
		}

	}

});

