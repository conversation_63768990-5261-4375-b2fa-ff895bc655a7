<?php

	$root="../..";
	header('Access-Control-Allow-Origin: *'); 
	require_once($root."/php/g.php");

	$func=$_POST['func'];

	switch ($func) {
		case 'saveSN':
			if (!isset($_POST['id'])  || empty($_POST['id'])) {
				exit();
			}

			$stmt=$conn->prepare("
							UPDATE bill_details SET serial_no=? WHERE id = ?
							");

			
			if ($stmt->execute([ $_POST['sn'],$_POST['id'] ])) {
				echo "OK";
			}else{
				
			}
		break;

		case 'update':

			

			$status = $bill->update_bill($_POST['id'],$_POST['data']);

			if ($status) {
				echo "OK";
			}else{
				echo "ER";
			}
		break;

		case 'delete':
			$status = $bill->delete_bill($_POST['id']);
			if ($status) {
				echo "OK";
			}else{
				echo "ER";
			}
		break;
		case 'get':
			$bill_data          =$bill->getBillById($_POST['id']);
			$bill_details       =$bill->getBillDetailsWithProducts($_POST['id']);
			$bill_total_payment =$bill->getBillTotalPayment($_POST['id']);
			$bill_payment       =$bill->getBillPayment($_POST['id']);
			$cus_data           =$customer->getCustomer($bill_data[0]['c_id'])[0];

			$data = [
				"bill_data"          =>$bill_data[0],
				"bill_details"       =>$bill_details,
				"bill_total_payment" =>amount($bill_total_payment),
				"bill_payment"       =>$bill_payment,
				"cus_data"           =>$cus_data,
				"price"              =>amount($bill_data[0]['price']),
			];
			echo json_encode($data);
			break;

		case 'updateBookingStatus':
			// Check if user has permission to manage bookings
			if (!can('manage-booking')) {
				echo "PERMISSION_DENIED";
				break;
			}

			if (!isset($_POST['bill_id']) || empty($_POST['bill_id']) ||
				!isset($_POST['status']) || empty($_POST['status'])) {
				echo "INVALID_PARAMS";
				break;
			}

			$bill_id = $_POST['bill_id'];
			$new_status = $_POST['status'];

			// Validate status value
			$allowed_statuses = ['Booked', 'Completed'];
			if (!in_array($new_status, $allowed_statuses)) {
				echo "INVALID_STATUS";
				break;
			}

			// Update booking status
			$stmt = $conn->prepare("UPDATE booking SET status = ? WHERE bill_id = ? AND deleted_01 = 0");

			if ($stmt->execute([$new_status, $bill_id])) {
				echo "OK";
			} else {
				echo "ERROR";
			}
			break;
		
		default:
			# code...
			break;
	}
	die();

?>