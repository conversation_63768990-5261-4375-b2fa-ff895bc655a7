<?php 
	$root="..";
	require($root."/php/g.php");
	
	$user->authorize('view-sales|manage-sales');

if(!isset($_GET['i'])|| empty($_GET['i'])){
	$redirect->to(404);

}

	$bill_id            =$_GET['i'];
	$bill_data          =$bill->getBillById($bill_id);
	$bill_details       =$bill->getBillDetailsWithProducts($bill_id);
	$bill_total_payment =$bill->getBillTotalPayment($bill_id);
	$bill_payment       =$bill->getBillPayment($bill_id);
	$bill_warranty      = $bill->getBillWarranty($bill_id);
	$bill_returns      = $bill->getBillReturns($bill_id);

	if(count($bill_data)==0){$redirect->to(404);}

	$cus_data           =$customer->getCustomer($bill_data[0]['c_id']);
	$created_by          =$user->get_user($bill_data[0]['created_by']);
	$updated_by       =$user->get_user($bill_data[0]['updated_by']);

	$refunds = $db->query("SELECT * FROM refunds WHERE bill_id = ? AND deleted_01= 0",[$bill_id ]);


	$page=[
		"title"=>tr("Invoice")." #".$bill_data[0]['id'],
		"active"=>"sales"
	];

	$page_nav=[
		"reload"=>1,
		"back"=>$app_url."/sales",
		"breadcrumb"=>[
			tr("Sales")=>$app_url."/sales",
			tr("View")=>$app_url."/sales",
		],
	];
 ?>

<!DOCTYPE html>
<html <?= $local ?>>
	<?php require($root."/inc/head.php") ?>
<body class=" <?= isset($page['class'])?$page['class']:'' ?> " >
	<style>
		.booking-info {
			background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
			border-left: 4px solid #007bff;
			transition: all 0.3s ease;
		}
		.booking-info:hover {
			box-shadow: 0 2px 8px rgba(0,123,255,0.15);
		}
		.booking-info .badge {
			font-size: 0.85em;
		}
		.booking-info .fas {
			margin-right: 4px;
		}
	</style>
	<?php require($root."/inc/header.php") ?>

		<div class="row">
			<div class="col">
				<?php $display->display_messages() ?>
				<div id="notify"></div>
				<div class="card">
					<div class="card-header">
						<div class="row">
							<div class="col-md-6 text-start">
								<h3><?= $lang['bill'] ?> #<?= escap($bill_data[0]['id']) ?></h3>
							</div>
							<div  class="col-md-6 text-end">
								<?php if (can('manage-sales')): ?>
										<button data-toggle="modal" data-target="#delete-bill" role="button" class="btn btn-danger" title="<?= $lang['delete'] ?>"><i class="fas fa-trash"></i></button>
										<a  href="<?= $app_url ?>/sales/edit.php?i=<?= escap($bill_data[0]['id']) ?>" class="btn btn-secondary text-white" title="<?= $lang['edit'] ?>"><?= tr("Edit") ?></a>
								<?php endif ?>
								<?php if (can("manage-refunds|add-refunds")): ?>
									<button onclick='window.open("<?= $app_url ?>/refunds/?v=new&i=<?= escap($bill_data[0]['id']) ?>&popup=1","_blank","height=820px width=700px");'   class="btn btn-secondary text-white" title="<?=tr("New refunds")?>"><?=tr("New refunds")?></button>
								<?php endif ?>
						
		
								
								<?php if ( amount(( floatval($bill_total_payment)-floatval($bill_data[0]['payable'])   ))<0): ?>
								<?php if (can("add-payments|manage-payments")): ?>
									<button role="button" onclick='window.open("<?= $app_url ?>/payments/?v=new&i=<?= escap($bill_data[0]['id']) ?>&popup=1","_blank","height=820px width=700px");' class="btn bg-info-800 text-white" title="<?= $lang['new_payment'] ?>" ><?= $lang['new_payment'] ?></button>
								<?php endif ?>
									
								<?php endif ?>

								<div class="btn-group justify-content-center">
									<a href="#" class="btn bg-success-800 dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><?= tr("Print") ?> <i class="fas fa-print"></i></a>

									<div class="dropdown-menu" x-placement="bottom-start" >
										<a href="<?= $app_url ?>/sales/prints/a4.print.php?i=<?= $bill_data[0]['id'] ?>" target="_blank"  class="dropdown-item"><?= tr("Print") ?> A4 A5</a>
										<a href="#" onclick="print_receipt(<?= escap($bill_data[0]['id']) ?>)" class="dropdown-item"><?= tr("Print") ?> <?= ($isRtl)?'ايصال':'receipt' ?></a>
										<a  href="prints/delivery.print.php?i=<?= $bill_data[0]['id'] ?>" target="_blank" class="dropdown-item"><?= ($isRtl)?'مذكرة تسليم':'Delivery Note' ?></a>
										<a href="<?= $app_url ?>/sales/prints/a4.print.php?i=<?= $bill_data[0]['id'] ?>&no_price" target="_blank"  class="dropdown-item"><?= tr("Print") ?> <?= ($isRtl)?'بدون سعر':'no price' ?></a>
								
									</div>
								</div>

						
								

								
							</div>
						</div>
					</div>
					<div class="card-body">
			
						
						<div class="row bg-light border py-3 px-3">
							<div class="col-md">
								<div class="row">
									<div class="col-2">
										<i class="fas fa-user fa-2x"></i>
									</div>
									<div class="col">
										<?php if ($cus_data[0]['id']==$info[0]['default_customer']): ?>
											<h5><?= escap($cus_data[0]['name']) ?></h5>
										<?php else: ?>
											<h5><a  target="_blank" href="<?= $app_url ?>/customers/?v=view&i=<?= escap($cus_data[0]['id']) ?>&popup=1"><?= escap($cus_data[0]['name']) ?></a></h5>
										<?php endif ?>
										
										<address class="mt-3">
											<p><?= escap($cus_data[0]['address']) ?></p>
											<?php if (strlen($cus_data[0]['phone'])>0): ?>
												<p><?= escap($lang['phone']) ?>: <?= escap($cus_data[0]['phone']) ?></p>
											<?php endif ?>
											

											<?php if ($cus_data[0]['id']!=$info[0]['default_customer']): ?>
											<p><?= escap($lang['balance']) ?>: <span dir="ltr"><?= $customer->getCustomerBalanceById($cus_data[0]['id']) ?></span></p>
											<?php endif ?>
											
										</address>
									</div>
								</div>
							</div>
							<div class="col-md">
								<div class="row">
									<div class="col-2">
										<i class="fas fa-building fa-2x"></i>
									</div>
									<div class="col">
										<h5><?= escap($created_by['name']) ?></h5>
										<address class="mt-3">
											<p></p>
											<p><?= escap($lang['phone']) ?>: <?= escap($created_by['phone']) ?></p>
										</address>
									</div>
								</div>
							</div>
							<div class="col-md">
								<div class="row">
									<div class="col-2">
										<i class="fas fa-file-invoice fa-2x"></i>
									</div>
									<div class="col">
										<h4 class="font-weight-black">#<?= escap($bill_data[0]['id']) ?></h4>
										<address class="mt-3">
											<p><?= escap($lang['added_on']) ?>: <span dir="ltr"><?= escap($bill_data[0]['created_at']) ?></span></p>
											<p><?= tr("Created by") ?>: <?= escap($created_by['name']) ?></p>
											<p><?= tr("Updated at") ?>: <span dir="ltr"><?= escap($bill_data[0]['updated_at']) ?></span></p>
											<p><?= escap($lang['updated_by']) ?>: <?= escap($updated_by['name']) ?></p>
										</address>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-4">
							<div class="col px-0 table-responsive">
								<table class="table table-sm border table-striped">
									<thead class="text-light bg-indigo" >
										<tr>
											<th>#</th>
											<th><?= escap($lang['pproduct']) ?></th>
											<th><?= tr("Serial no") ?></th>
											<th class="text-center"><?= escap($lang['p_quantity']) ?></th>
											<th class="text-center"><?= escap($lang['p_price']) ?> (<?= escap($currency) ?>)</th>
											<th class="text-center"><?= tr("Tax") ?></th>
											<th class="text-center"><?= escap($lang['total']) ?> (<?= escap($currency) ?>)</th>
										</tr>
									</thead>
									<tbody>
										<?php $n=1; $tax=0; foreach ($bill_details as $ro): ?>
											<tr <?= ($ro['p_type']=="I")?'class="bg-light"':'class=""' ?>>
												<td><?= $n ?></td>
												<?php if ($ro['p_type']=="I"): ?>
													<td><?= pname($ro['name'],$ro['name2']) ?>
														
													</td>
												<?php else: ?>
													<td><a href="<?= $app_url ?>/products/view.php?i=<?= $ro['p_id'] ?>"><?= pname($ro['name'],$ro['name2']) ?></a>

														
														<?php if ($ro['booking']==1): ?>
															<br>
															<?php
															// Enhanced booking information query
															$book = $db->row("SELECT b.*, c.name as customer_name, c.phone as customer_phone
																			  FROM booking b
																			  LEFT JOIN customers c ON b.customer_id = c.id
																			  WHERE b.p_id = ? AND b.bill_id = ? AND b.deleted_01 = 0",
																			  [$ro['p_id'], $bill_id]);

															if ($book): ?>
																<div class="booking-info mt-2 p-2 border rounded bg-light">
																	<div class="row">
																		<div class="col-md-6">
																			<strong><?= tr("Booking Period") ?>:</strong><br>
																			<span dir="ltr" class="text-monospace">
																				<i class="fas fa-calendar-alt"></i> <?= $book['start_date'] ?>
																				<i class="fas fa-arrow-right mx-1"></i>
																				<?= $book['end_date'] ?>
																			</span>
																		</div>
																		<div class="col-md-6">
																			<strong><?= tr("Status") ?>:</strong><br>
																			<?php if ($book['status'] == 'Completed'): ?>
																				<span class="badge badge-success">
																					<i class="fas fa-check-circle"></i> <?= tr("Completed") ?>
																				</span>
																			<?php else: ?>
																				<span class="badge badge-warning">
																					<i class="fas fa-clock"></i> <?= tr("Booked") ?>
																				</span>
																			<?php endif ?>

																			<?php if ($book['sms_sent'] == 1): ?>
																				<span class="badge badge-info ml-1">
																					<i class="fas fa-sms"></i> <?= tr("SMS Sent") ?>
																				</span>
																			<?php endif ?>
																		</div>
																	</div>

																	<?php if (can('manage-booking') && $book['status'] != 'Completed'): ?>
																		<div class="row mt-2">
																			<div class="col-12">
																				<button onclick="updateBookingStatus(<?= $bill_id ?>, 'Completed')"
																						class="btn btn-sm btn-success">
																					<i class="fas fa-check"></i> <?= tr("Mark as Completed") ?>
																				</button>
																			</div>
																		</div>
																	<?php endif ?>
																</div>
															<?php endif ?>
														<?php endif ?>
													</td>
												<?php endif ?>
												
												<td><button class="btn btn-light btn-sm" data-toggle="modal" data-target="#sn-modal-<?= escap($ro['id']) ?>"><i class="far fa-edit"></i></button> <?= escap(mb_substr($ro['serial_no'], 0, 15)) ?><?= (strlen($ro['serial_no'])>15)?'...':'' ?></td>
												<td class="text-center text-monospace"><?= escap($ro['quantity']) ?></td>
												<td class="text-center text-monospace"><?= escap(amount($ro['price'])) ?></td>
												<td class="text-center text-monospace"><?= $ro['tax_percent'] ?>%</td>
												<td class="text-center text-monospace"><?= amount($ro['quantity']*($ro['price']+($ro['price']*$ro['tax_percent']/100))   ) ?></td>
											</tr>
										<?php $tax+=($ro['quantity']*$ro['price']*$ro['tax_percent']/100); $n++; endforeach ?>
										<tr>
											<th colspan="6" class="text-end px-5"><?= tr("Subtotal") ?> (<?= escap($currency) ?>)</th>
											<th class="text-center text-monospace"><?= escap(amount($bill_data[0]['price']-$tax)) ?></th>
										</tr>
										<tr>
											<th colspan="6" class="text-end px-5"><?= tr("Total tax") ?> (<?= escap($currency) ?>)</th>
											<th class="text-center text-monospace"><?= amount($tax) ?></th>
										</tr>
										<tr>
											<th colspan="6" class="text-end px-5"><?= $lang['discount'] ?> (<?= $currency ?>)</th>
											<th class="text-center text-monospace">
												<?= escap(amount($bill_data[0]['discount'])) ?>
											</th>
										</tr>
										<tr>
											<th colspan="6" class="text-end px-5"><?= escap($lang['payable']) ?> (<?= escap($currency) ?>)</th>
											
											<th class="text-center text-monospace"><?= amount($bill_data[0]['payable']) ?></th>
										</tr>
										<tr>
											<th colspan="6" class="text-end px-5"><?= escap($lang['paid']) ?> (<?= escap($currency) ?>)</th>
											<th class="text-center text-monospace"><?= escap(amount($bill_total_payment)) ?></th>
										</tr>
										<tr>
											<th colspan="6" class="text-end px-5">
												
												<h5 class="font-weight-black m-0"><?= tr("Total") ?> (<?= escap($currency) ?>)</h5>
											</th>
											<th dir="ltr"  class="text-center text-monospace font-weight-black">
												<h5 class="font-weight-black m-0"><?= amount($bill_data[0]['payable'] - $bill_total_payment) ?></h5>
											</th>
										</tr>

									</tbody>
								</table>
							</div>
						</div>
						<?php if (count($bill_payment)): ?>
							<div class="row mt-4">
								<div class="col px-0 ">
									<h5><?= tr("Payments") ?></h5>
									<div class="table-responsive">
										<table class="table table-sm border">
										<thead class="text-light bg-success" >
											<tr>
												<th><?= $lang['date'] ?></th>
												<th><?= $lang['pay_by'] ?></th>
												<th class="text-center"><?= $lang['amount'] ?> (<?= escap($currency) ?>)</th>
											
												<th ><?= $lang['added_by'] ?></th>
												<th class="text-center"><?= $lang['status'] ?></th>
												
											</tr>
										</thead>
										<tbody>
											<?php foreach ($bill_payment as $ro): ?>
												<!-- Payment amount can be 0 if payment form POS  -->
												<?php if ($ro['pay_amount']!=0): ?>
												
													<tr>
														<td dir="ltr" class="text-monospace"><?= $ro['created_at'] ?></td>
														<td><?= tr($ro['pay_by']) ?></td>
														<td class="text-center text-monospace"><?= amount($ro['pay_amount']) ?></td>
													
														<td>
															<?= escap($user->get($ro['created_by'])->name) ?>
															<?php if ($ro['created_at']!=$ro['updated_at']): ?>
																<br>
																<small class="text-muted font-size-xs"><?= tr("Update") ?>: <?= escap($user->get($ro['updated_by'])->name) ?></small>
															<?php endif ?>
														</td>
														<?php if ($ro['received_01']==1): ?>
															<td class="text-center"><span class="badge badge-success"><?= $lang['received'] ?></span></td>
														<?php else: ?>
															<td class="text-center"><span class="badge badge-danger"><?= $ro['due_date'] ?></span> <button onclick="setBillChequeAsReceived(<?= escap($ro['id']) ?>)" class="btn btn-outline-secondary btn-sm" title="<?= escap($lang['received']) ?>"><i class="fas fa-check"></i></button></td>
														<?php endif ?>
											
													</tr>
												<?php endif ?>
												
											<?php endforeach ?>
										</tbody>
									</table>
									</div>
								</div>
							</div>
						<?php endif ?>
						

						<?php if (strlen($bill_data[0]['descr'])): ?>
							<div class="row mt-4">
								<div class="col">
									<p><strong><?= escap($lang['note']) ?>:</strong></p>
									<p><?= escap($bill_data[0]['descr']) ?></p>
								</div>
							</div>
						<?php endif ?>

						
						<?php if (count($refunds)>0): ?>
							<div class="row">
								<div class="col m-0 p-0">
									<h5><?= tr("Refunds") ?></h5>
									<div class="table-responsive">
										<table class="table table-sm border table-striped">
								<thead class="bg-warning text-white">
									<tr>
										<th>#</th>
										<th><?= tr("Item") ?></th>
										<th><?= tr("Qunatity") ?></th>
										<th><?= tr("Refund amount") ?></th>
									</tr>
								</thead>
								<tbody>
									<?php foreach ($refunds as $key => $refund): ?>
										<tr>
											<td>
												<?php if (can("manage-refunds")): ?>
													<a target="_blank" href="<?= $app_url ?>/refunds/?v=edit&i=<?= $refund['id'] ?>"><?= $refund['id'] ?></a>
												<?php else: ?>
													<?= $refund['id'] ?>
												<?php endif ?>
												
											</td>
											<td>
												<?= $product->getProductById($refund['p_id'])[0]['name'] ?>
											</td>
											<td>
												<?= $refund['quantity'] ?>
											</td>
											<td>
												<?= amount($refund['price']) ?>
											</td>
										</tr>
									<?php endforeach ?>
								</tbody>

							</table>
									</div>
									
								</div>
							</div>
					
						<?php endif ?>
						
			
					

				


					</div>
				</div>
			</div>
		</div>


	<?php foreach ($bill_details as $ro): ?>
		<!-- sn-modal -->
		<div class="modal fade" id="sn-modal-<?= escap($ro['id']) ?>" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
		  <div class="modal-dialog" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h5 class="modal-title" id="exampleModalLabel"><?= escap($ro['name']) ?></h5>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
		        <div class="form-group">
		        	<textarea class="form-control" id="sn-text-<?= escap($ro['id']) ?>" cols="30" rows="3"><?= escap($ro['serial_no']) ?></textarea>	
		        </div>
		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= escap($lang['close']) ?></button>
		        <button onclick="saveSN(<?= escap($ro['id']) ?>,$('#sn-text-<?= escap($ro['id']) ?>').val())" type="button" class="btn btn-primary"><?= escap($lang['save']) ?></button>
		      </div>
		    </div>
		  </div>
		</div>
	<?php endforeach ?>



	<?php require_once($root."/inc/footer.php") ?>
	<?php require_once($root."/inc/scripts.php") ?>
	<?php require_once("inc/new-payment-modal.inc.php") ?>



	<!-- delete -->
	<div class="modal fade" id="delete-bill" tabindex="-1" role="dialog" aria-labelledby="delete-billLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
	  <div class="modal-dialog modal-dialog-centered" role="document">
	    <div class="modal-content ">
	      <div class="modal-header">
	        <h5 class="modal-title" id="delete-billLabel"><?= $lang['you_sure'] ?></h5>
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
	          <span aria-hidden="true" style="font-size: 24px">&times;</span>
	        </button>
	      </div>
	      <div class="modal-body">
	        <?= $lang['delete_element'] ?>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= $lang['close'] ?></button>
	        <button type="button" onclick="deleteBill()"class="btn btn-danger" data-dismiss="modal"><?= $lang['yes'] ?></button>
	      </div>
	    </div>
	  </div>
	</div>

	<script>
		function setBillChequeAsReceived(id){
			$.post('<?= $app_url ?>/sales/ajax/bill_payment.php', 
				{
					"function": 'setBillChequeAsReceived',
					"id":id
				}, 
				function(data) {
					console.log(data)
					if (data=="OK") {
						bAlert("<?= escap($lang['wents_success']) ?>","success")
						location.reload()
					}
				}
			);
		}

		function saveSN(id,sn){
			console.log(id)
			console.log(sn)
			$.post('<?= $app_url ?>/sales/ajax/bills-details.php', 
				{
					'func': 'saveSN',
					'id':id,
					'sn':sn,
				}, function(data, textStatus, xhr) {
					console.log(data)
					if (data=="OK") {
						bAlert("<?= escap($lang['wents_success']) ?>","success")
						location.reload()
					}
				}
			);
		}


		function deleteBill(){
			$.post('<?= $app_url ?>/sales/ajax/bills-details.php', 
				{
					'func': 'delete',
					'id':'<?= escap($bill_data[0]['id']) ?>',
				}, function(data) {
					console.log(data)
					if (data=="OK") {
						bAlert("<?= escap($lang['wents_success']) ?>","success")
						location.replace("<?= $app_url ?>/sales/")
					}
				}
			);
		}

		function updateBookingStatus(billId, newStatus) {
			// Confirmation dialog
			var confirmMessage = newStatus === 'Completed' ?
				'<?= tr("Are you sure you want to mark this booking as completed?") ?>' :
				'<?= tr("Are you sure you want to change the booking status?") ?>';

			if (!confirm(confirmMessage)) {
				return;
			}

			$.post('<?= $app_url ?>/sales/ajax/bills-details.php', {
				'func': 'updateBookingStatus',
				'bill_id': billId,
				'status': newStatus
			}, function(data) {
				console.log(data);
				if (data === "OK") {
					bAlert("<?= tr('Booking status updated successfully') ?>", "success");
					location.reload();
				} else if (data === "PERMISSION_DENIED") {
					bAlert("<?= tr('You do not have permission to manage bookings') ?>", "error");
				} else if (data === "INVALID_PARAMS") {
					bAlert("<?= tr('Invalid parameters provided') ?>", "error");
				} else if (data === "INVALID_STATUS") {
					bAlert("<?= tr('Invalid booking status') ?>", "error");
				} else {
					bAlert("<?= tr('Error updating booking status') ?>", "error");
				}
			}).fail(function() {
				bAlert("<?= tr('Network error occurred') ?>", "error");
			});
		}

		function print_receipt(id){
					<?php 
					$print = $db->row("SELECT * FROM print_settings ");
				switch ($print['pos_mode']) {
					case 'host': ?>
						$.get('<?= $app_url ?>/sales/prints/receipt.pdf.php?i='+id, function(data) {
					
						
							$.post('<?= $print['pos_host'] ?>/print/', {'html': data}, function(data, textStatus, xhr) {
								/*optional stuff to do after success */
							});
						});
					<?php break; 
					case 'direct':?>
						$.get('<?= $app_url ?>/sales/prints/receipt.pdf.php?i='+id, function(data) {
							/*optional stuff to do after success */
						});
					<?php break;
					
					 default:?>
							window.open("<?= $app_url ?>/sales/prints/receipt.print.php?i="+id,"_blank","height=720px width=1330px");
					<?php break;
				} ?>
				
			}

		
	</script>
</body>
</html>